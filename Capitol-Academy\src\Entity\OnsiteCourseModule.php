<?php

namespace App\Entity;

use App\Repository\OnsiteCourseModuleRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OnsiteCourseModuleRepository::class)]
#[ORM\Table(name: 'onsite_course_module')]
class OnsiteCourseModule
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 10)]
    private ?string $code = null;

    #[ORM\Column(length: 255)]
    private ?string $title = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $learning_outcomes = [];

    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $features = [];

    #[ORM\Column(name: 'sort_order')]
    private ?int $order = 0;

    #[ORM\Column]
    private ?bool $is_active = true;

    #[ORM\ManyToOne(inversedBy: 'modules')]
    #[ORM\JoinColumn(name: 'onsite_course_id', nullable: false)]
    private ?OnsiteCourse $onsiteCourse = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $created_at = null;

    public function __construct()
    {
        $this->created_at = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getLearningOutcomes(): ?array
    {
        return $this->learning_outcomes;
    }

    public function setLearningOutcomes(?array $learning_outcomes): static
    {
        $this->learning_outcomes = $learning_outcomes;

        return $this;
    }

    public function getFeatures(): ?array
    {
        return $this->features;
    }

    public function setFeatures(?array $features): static
    {
        $this->features = $features;

        return $this;
    }

    public function getOrder(): ?int
    {
        return $this->order;
    }

    public function setOrder(int $order): static
    {
        $this->order = $order;

        return $this;
    }

    // Keep legacy method for backward compatibility
    public function getSortOrder(): ?int
    {
        return $this->order;
    }

    public function setSortOrder(int $sort_order): static
    {
        $this->order = $sort_order;

        return $this;
    }



    public function isActive(): ?bool
    {
        return $this->is_active;
    }

    public function setActive(bool $is_active): static
    {
        $this->is_active = $is_active;

        return $this;
    }

    public function getOnsiteCourse(): ?OnsiteCourse
    {
        return $this->onsiteCourse;
    }

    public function setOnsiteCourse(?OnsiteCourse $onsiteCourse): static
    {
        $this->onsiteCourse = $onsiteCourse;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->created_at;
    }

    public function setCreatedAt(\DateTimeImmutable $created_at): static
    {
        $this->created_at = $created_at;

        return $this;
    }



    /**
     * Check if module has content
     */
    public function hasContent(): bool
    {
        return !empty($this->description) || 
               !empty($this->learning_outcomes) || 
               !empty($this->features);
    }

    /**
     * Get module status for display
     */
    public function getStatusBadge(): string
    {
        return $this->is_active ? 
            '<span class="badge bg-success">Active</span>' : 
            '<span class="badge bg-secondary">Inactive</span>';
    }
}
