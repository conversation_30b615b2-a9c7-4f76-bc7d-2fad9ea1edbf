{% extends 'admin/base.html.twig' %}

{% block title %}Create Onsite Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Onsite Course{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_onsite_courses') }}">Onsite Courses</a></li>
<li class="breadcrumb-item active">Create Onsite Course</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-graduation-cap mr-3" style="font-size: 2rem;"></i>
                        Create New Onsite Course
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Onsite Courses Button -->
                        <a href="{{ path('admin_onsite_courses') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Onsite Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <form method="post" id="onsite-course-form" class="needs-validation" enctype="multipart/form-data" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('onsite_course_create') }}">
            <input type="hidden" name="is_active" value="1">
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <!-- Course Code and Title Row -->
                        <div class="row">
                            <!-- Course Code -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="code" class="form-label">
                                        <i class="fas fa-hashtag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                        Onsite Course Code <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="code"
                                           name="code"
                                           placeholder="e.g., OSC001, TRAD101"
                                           required
                                           maxlength="10">
                                    <div class="invalid-feedback">
                                        Please provide a valid onsite course code.
                                    </div>
                                </div>
                            </div>

                            <!-- Course Title -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                                        Onsite Course Title <span class="text-danger">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="title"
                                           name="title"
                                           placeholder="Enter onsite course title"
                                           required
                                           maxlength="255">
                                    <div class="invalid-feedback">
                                        Please provide a valid onsite course title.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-group mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left" style="color: #007bff; margin-right: 0.5rem;"></i>
                                Description
                            </label>
                            <textarea class="form-control"
                                      id="description"
                                      name="description"
                                      rows="4"
                                      placeholder="Enter onsite course description"
                                      maxlength="2000"></textarea>
                        </div>

                        <!-- Category and Level Row -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="category" class="form-label">
                                        <i class="fas fa-tags" style="color: #007bff; margin-right: 0.5rem;"></i>
                                        Category <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="category" name="category" required>
                                        <option value="">Select category</option>
                                        {% for category in categories %}
                                            <option value="{{ category.name }}">{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a category.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="level" class="form-label">
                                        <i class="fas fa-layer-group" style="color: #007bff; margin-right: 0.5rem;"></i>
                                        Difficulty Level
                                    </label>
                                    <select class="form-select" id="level" name="level">
                                        <option value="">Select level</option>
                                        <option value="Beginner">Beginner</option>
                                        <option value="Intermediate">Intermediate</option>
                                        <option value="Advanced">Advanced</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Duration and Price Row -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="duration" class="form-label">
                                        <i class="fas fa-clock" style="color: #007bff; margin-right: 0.5rem;"></i>
                                        Duration (minutes)
                                    </label>
                                    <input type="number"
                                           class="form-control"
                                           id="duration"
                                           name="duration"
                                           placeholder="Enter duration in minutes"
                                           min="0">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="price" class="form-label">
                                        <i class="fas fa-dollar-sign" style="color: #007bff; margin-right: 0.5rem;"></i>
                                        Price (USD) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number"
                                           class="form-control"
                                           id="price"
                                           name="price"
                                           placeholder="0.00"
                                           step="0.01"
                                           min="0"
                                           required>
                                    <div class="invalid-feedback">
                                        Please provide a valid price.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Image Uploads Row -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="thumbnail_image" class="form-label">
                                        <i class="fas fa-image" style="color: #007bff; margin-right: 0.5rem;"></i>
                                        Thumbnail Image
                                    </label>
                                    <input type="file"
                                           class="form-control"
                                           id="thumbnail_image"
                                           name="thumbnail_image"
                                           accept="image/*">
                                    <div class="form-text">Upload a thumbnail image (300x200px recommended). Max size: 5MB</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="banner_image" class="form-label">
                                        <i class="fas fa-panorama" style="color: #007bff; margin-right: 0.5rem;"></i>
                                        Banner Image
                                    </label>
                                    <input type="file"
                                           class="form-control"
                                           id="banner_image"
                                           name="banner_image"
                                           accept="image/*">
                                    <div class="form-text">Upload a banner image (1200x400px recommended). Max size: 10MB</div>
                                </div>
                            </div>
                        </div>

                        <!-- Modules Checkbox -->
                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="has_modules" name="has_modules" value="1">
                                <label class="form-check-label" for="has_modules">
                                    <i class="fas fa-puzzle-piece" style="color: #007bff; margin-right: 0.5rem;"></i>
                                    Enable Modules
                                </label>
                                <div class="form-text">Check this to enable modular structure for this onsite course</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Footer -->
            <div class="card-footer" style="background: #f8f9fa; border-top: 1px solid #dee2e6; padding: 1.5rem;">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="{{ path('admin_onsite_courses') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-success" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border: none; padding: 0.75rem 2rem;">
                        <i class="fas fa-save me-2"></i>Create Onsite Course
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Dynamic Learning Outcomes Management
    $(document).on('click', '.add-learning-outcome', function() {
        var container = $('#learning-outcomes-container');
        var newItem = `
            <div class="input-group mb-2 learning-outcome-item">
                <input type="text"
                       class="form-control enhanced-field"
                       name="learning_outcomes[]"
                       placeholder="Enter a learning outcome..."
                       required
                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-learning-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0;">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-learning-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove learning outcome
    $(document).on('click', '.remove-learning-outcome', function() {
        $(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    $(document).on('click', '.add-feature', function() {
        var container = $('#features-container');
        var newItem = `
            <div class="input-group mb-2 feature-item">
                <input type="text"
                       class="form-control enhanced-field"
                       name="features[]"
                       placeholder="e.g., Live instructor sessions, Downloadable resources..."
                       required
                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0;">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    // Remove feature
    $(document).on('click', '.remove-feature', function() {
        $(this).closest('.feature-item').remove();
    });

    // Thumbnail image preview
    const thumbnailInput = document.getElementById('thumbnail_image');
    if (thumbnailInput) {
        thumbnailInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewContainer = document.getElementById('thumbnail-preview');
            const previewImg = document.getElementById('thumbnail-preview-img');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    previewContainer.style.display = 'flex';
                };
                reader.readAsDataURL(file);
            } else {
                previewContainer.style.display = 'none';
            }
        });
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type="submit"]');

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Create Onsite Course';
                        }

                        // Show help text when validation fails
                        $('.help-text').show();
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Course...';
                        }

                        // Hide help text when form is valid
                        $('.help-text').hide();
                    }

                    // Add validation classes for styling
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
});
</script>
{% endblock %}
