<?php

namespace ContainerHf8wzxQ;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_Bki8G0JService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.bki8G0J' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.bki8G0J'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'video' => ['privates', '.errored..service_locator.bki8G0J.App\\Entity\\Video', NULL, 'Cannot autowire service ".service_locator.bki8G0J": it needs an instance of "App\\Entity\\Video" but this type has been excluded in "config/services.yaml".'],
        ], [
            'video' => 'App\\Entity\\Video',
        ]);
    }
}
