<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/remote_courses/index.html.twig */
class __TwigTemplate_a7d128151b2dd6101c9513c814e887e1 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/remote_courses/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/remote_courses/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Remote Courses Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Remote Courses Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Remote Courses</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Remote Courses Management", "page_icon" => "fas fa-video", "search_placeholder" => "Search remote courses...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_create"), "text" => "Add New Remote Course", "icon" => "fas fa-plus"], "stats" => [["title" => "Total Remote Courses", "value" => CoreExtension::getAttribute($this->env, $this->source,         // line 25
(isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 25, $this->source); })()), "total", [], "any", false, false, false, 25), "icon" => "fas fa-video", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active Courses", "value" => CoreExtension::getAttribute($this->env, $this->source,         // line 32
(isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 32, $this->source); })()), "active", [], "any", false, false, false, 32), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Total Enrollments", "value" => CoreExtension::getAttribute($this->env, $this->source,         // line 39
(isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 39, $this->source); })()), "total_enrollments", [], "any", false, false, false, 39), "icon" => "fas fa-users", "color" => "#17a2b8", "gradient" => "linear-gradient(135deg, #17a2b8 0%, #138496 100%)"], ["title" => "Recent (30 days)", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 46
(isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 46, $this->source); })()), function ($__course__) use ($context, $macros) { $context["course"] = $__course__; return (CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 46, $this->source); })()), "createdAt", [], "any", false, false, false, 46) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["course"]) || array_key_exists("course", $context) ? $context["course"] : (function () { throw new RuntimeError('Variable "course" does not exist.', 46, $this->source); })()), "createdAt", [], "any", false, false, false, 46) > $this->extensions['Twig\Extension\CoreExtension']->convertDate("-30 days"))); })), "icon" => "fas fa-clock", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 53
        yield "
";
        // line 54
        yield from $this->load("admin/remote_courses/index.html.twig", 54, "199683137")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 54, $this->source); })())));
        // line 101
        yield "
<!-- Status Toggle Modal -->
<div class=\"modal fade\" id=\"statusToggleModal\" tabindex=\"-1\" aria-labelledby=\"statusToggleModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"statusToggleModalLabel\">Confirm Status Change</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to <span id=\"statusAction\"></span> the remote course \"<span id=\"statusCourseTitle\"></span>\"?</p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-primary\" id=\"confirmStatusToggle\">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class=\"modal fade\" id=\"deleteModal\" tabindex=\"-1\" aria-labelledby=\"deleteModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"deleteModalLabel\">Confirm Deletion</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to delete the remote course \"<span id=\"deleteCourseTitle\"></span>\"?</p>
                <p class=\"text-danger\"><strong>Warning:</strong> This action cannot be undone. All chapters and video assignments will be permanently deleted.</p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger\" id=\"confirmDelete\">Delete Remote Course</button>
            </div>
        </div>
    </div>
</div>

<script>
// Status toggle functionality
function toggleRemoteCourseStatus(courseId, courseTitle, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('statusCourseTitle').textContent = courseTitle;

    const modal = new bootstrap.Modal(document.getElementById('statusToggleModal'));
    modal.show();

    document.getElementById('confirmStatusToggle').onclick = function() {
        fetch(`";
        // line 152
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_toggle_status", ["id" => "__ID__"]);
        yield "`.replace('__ID__', courseId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                '_token': '";
        // line 159
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("toggle_status"), "html", null, true);
        yield "'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to update course status'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the course status');
        });

        modal.hide();
    };
}

// Delete functionality
function deleteRemoteCourse(courseId, courseTitle) {
    document.getElementById('deleteCourseTitle').textContent = courseTitle;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();

    document.getElementById('confirmDelete').onclick = function() {
        fetch(`";
        // line 187
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_delete", ["id" => "__ID__"]);
        yield "`.replace('__ID__', courseId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                '_token': '";
        // line 194
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("delete"), "html", null, true);
        yield "'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to delete course'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the course');
        });

        modal.hide();
    };
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/remote_courses/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  268 => 194,  258 => 187,  227 => 159,  217 => 152,  164 => 101,  162 => 54,  159 => 53,  157 => 46,  156 => 39,  155 => 32,  154 => 25,  153 => 13,  140 => 12,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Remote Courses Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Remote Courses Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Remote Courses</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Remote Courses Management',
    'page_icon': 'fas fa-video',
    'search_placeholder': 'Search remote courses...',
    'create_button': {
        'url': path('admin_remote_course_create'),
        'text': 'Add New Remote Course',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Remote Courses',
            'value': stats.total,
            'icon': 'fas fa-video',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active Courses',
            'value': stats.active,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Total Enrollments',
            'value': stats.total_enrollments,
            'icon': 'fas fa-users',
            'color': '#17a2b8',
            'gradient': 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': courses|filter(course => course.createdAt and course.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Instructor'},
            {'text': 'Chapters'},
            {'text': 'Videos'},
            {'text': 'Price'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 250px;'}
        ] %}

        {% set table_rows = [] %}
        {% for course in courses %}
            {% set table_rows = table_rows|merge([{
                'cells': [
                    {'content': '<span class=\"badge bg-primary\">' ~ course.code ~ '</span>'},
                    {'content': '<strong>' ~ course.title ~ '</strong>'},
                    {'content': course.category ?: '<span class=\"text-muted\">Not specified</span>'},
                    {'content': course.instructor ?: '<span class=\"text-muted\">Not assigned</span>'},
                    {'content': '<span class=\"badge bg-secondary\">' ~ course.chapters.count ~ ' chapters</span>'},
                    {'content': '<span class=\"badge bg-info\">' ~ course.totalVideos ~ ' videos</span>'},
                    {'content': '<strong>' ~ course.formattedPrice ~ '</strong>'},
                    {'content': course.statusBadge|raw},
                    {
                        'content': '<div class=\"btn-group\" role=\"group\">
                            <a href=\"' ~ path('admin_remote_course_preview', {'id': course.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Remote Course\"><i class=\"fas fa-eye\"></i></a>
                            <a href=\"' ~ path('admin_remote_course_edit', {'id': course.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Remote Course\"><i class=\"fas fa-edit\"></i></a>
                            <a href=\"' ~ path('admin_remote_course_chapters', {'id': course.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #6f42c1; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Manage Chapters\"><i class=\"fas fa-list\"></i></a>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (course.isActive ? '#6c757d' : '#28a745') ~ '; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (course.isActive ? 'Deactivate' : 'Activate') ~ ' Remote Course\" onclick=\"toggleRemoteCourseStatus(' ~ course.id ~ ', \\'' ~ course.title ~ '\\', ' ~ course.isActive ~ ')\"><i class=\"fas fa-' ~ (course.isActive ? 'pause' : 'play') ~ '\"></i></button>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Remote Course\" onclick=\"deleteRemoteCourse(' ~ course.id ~ ', \\'' ~ course.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                        </div>'
                    }
                ]
            }]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'empty_message': 'No remote courses found. <a href=\"' ~ path('admin_remote_course_create') ~ '\" class=\"text-primary\">Create your first remote course</a>.'
        } %}
    {% endblock %}
{% endembed %}

<!-- Status Toggle Modal -->
<div class=\"modal fade\" id=\"statusToggleModal\" tabindex=\"-1\" aria-labelledby=\"statusToggleModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"statusToggleModalLabel\">Confirm Status Change</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to <span id=\"statusAction\"></span> the remote course \"<span id=\"statusCourseTitle\"></span>\"?</p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-primary\" id=\"confirmStatusToggle\">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class=\"modal fade\" id=\"deleteModal\" tabindex=\"-1\" aria-labelledby=\"deleteModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"deleteModalLabel\">Confirm Deletion</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to delete the remote course \"<span id=\"deleteCourseTitle\"></span>\"?</p>
                <p class=\"text-danger\"><strong>Warning:</strong> This action cannot be undone. All chapters and video assignments will be permanently deleted.</p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger\" id=\"confirmDelete\">Delete Remote Course</button>
            </div>
        </div>
    </div>
</div>

<script>
// Status toggle functionality
function toggleRemoteCourseStatus(courseId, courseTitle, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('statusCourseTitle').textContent = courseTitle;

    const modal = new bootstrap.Modal(document.getElementById('statusToggleModal'));
    modal.show();

    document.getElementById('confirmStatusToggle').onclick = function() {
        fetch(`{{ path('admin_remote_course_toggle_status', {'id': '__ID__'}) }}`.replace('__ID__', courseId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                '_token': '{{ csrf_token('toggle_status') }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to update course status'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the course status');
        });

        modal.hide();
    };
}

// Delete functionality
function deleteRemoteCourse(courseId, courseTitle) {
    document.getElementById('deleteCourseTitle').textContent = courseTitle;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();

    document.getElementById('confirmDelete').onclick = function() {
        fetch(`{{ path('admin_remote_course_delete', {'id': '__ID__'}) }}`.replace('__ID__', courseId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                '_token': '{{ csrf_token('delete') }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to delete course'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the course');
        });

        modal.hide();
    };
}
</script>
{% endblock %}
", "admin/remote_courses/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\remote_courses\\index.html.twig");
    }
}


/* admin/remote_courses/index.html.twig */
class __TwigTemplate_a7d128151b2dd6101c9513c814e887e1___199683137 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 54
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/remote_courses/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/remote_courses/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 54);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 55
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 56
        yield "        <!-- Standardized Table -->
        ";
        // line 57
        $context["table_headers"] = [["text" => "Code"], ["text" => "Title"], ["text" => "Category"], ["text" => "Instructor"], ["text" => "Chapters"], ["text" => "Videos"], ["text" => "Price"], ["text" => "Status"], ["text" => "Actions", "style" => "width: 250px;"]];
        // line 68
        yield "
        ";
        // line 69
        $context["table_rows"] = [];
        // line 70
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["courses"]) || array_key_exists("courses", $context) ? $context["courses"] : (function () { throw new RuntimeError('Variable "courses" does not exist.', 70, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["course"]) {
            // line 71
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 71, $this->source); })()), [["cells" => [["content" => (("<span class=\"badge bg-primary\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 73
$context["course"], "code", [], "any", false, false, false, 73)) . "</span>")], ["content" => (("<strong>" . CoreExtension::getAttribute($this->env, $this->source,             // line 74
$context["course"], "title", [], "any", false, false, false, 74)) . "</strong>")], ["content" => ((CoreExtension::getAttribute($this->env, $this->source,             // line 75
$context["course"], "category", [], "any", false, false, false, 75)) ? (CoreExtension::getAttribute($this->env, $this->source, $context["course"], "category", [], "any", false, false, false, 75)) : ("<span class=\"text-muted\">Not specified</span>"))], ["content" => ((CoreExtension::getAttribute($this->env, $this->source,             // line 76
$context["course"], "instructor", [], "any", false, false, false, 76)) ? (CoreExtension::getAttribute($this->env, $this->source, $context["course"], "instructor", [], "any", false, false, false, 76)) : ("<span class=\"text-muted\">Not assigned</span>"))], ["content" => (("<span class=\"badge bg-secondary\">" . CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source,             // line 77
$context["course"], "chapters", [], "any", false, false, false, 77), "count", [], "any", false, false, false, 77)) . " chapters</span>")], ["content" => (("<span class=\"badge bg-info\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 78
$context["course"], "totalVideos", [], "any", false, false, false, 78)) . " videos</span>")], ["content" => (("<strong>" . CoreExtension::getAttribute($this->env, $this->source,             // line 79
$context["course"], "formattedPrice", [], "any", false, false, false, 79)) . "</strong>")], ["content" => CoreExtension::getAttribute($this->env, $this->source,             // line 80
$context["course"], "statusBadge", [], "any", false, false, false, 80)], ["content" => (((((((((((((((((((((("<div class=\"btn-group\" role=\"group\">
                            <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_preview", ["id" => CoreExtension::getAttribute($this->env, $this->source,             // line 83
$context["course"], "id", [], "any", false, false, false, 83)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Remote Course\"><i class=\"fas fa-eye\"></i></a>
                            <a href=\"") . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_edit", ["id" => CoreExtension::getAttribute($this->env, $this->source,             // line 84
$context["course"], "id", [], "any", false, false, false, 84)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Remote Course\"><i class=\"fas fa-edit\"></i></a>
                            <a href=\"") . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_chapters", ["id" => CoreExtension::getAttribute($this->env, $this->source,             // line 85
$context["course"], "id", [], "any", false, false, false, 85)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #6f42c1; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Manage Chapters\"><i class=\"fas fa-list\"></i></a>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 86
$context["course"], "isActive", [], "any", false, false, false, 86)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"))) . "; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 86)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"))) . " Remote Course\" onclick=\"toggleRemoteCourseStatus(") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "id", [], "any", false, false, false, 86)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 86)) . "', ") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 86)) . ")\"><i class=\"fas fa-") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["course"], "isActive", [], "any", false, false, false, 86)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("pause") : ("play"))) . "\"></i></button>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Remote Course\" onclick=\"deleteRemoteCourse(") . CoreExtension::getAttribute($this->env, $this->source,             // line 87
$context["course"], "id", [], "any", false, false, false, 87)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["course"], "title", [], "any", false, false, false, 87)) . "')\"><i class=\"fas fa-trash\"></i></button>
                        </div>")]]]]);
            // line 92
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['course'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 93
        yield "
        ";
        // line 94
        yield from $this->load("components/admin_table.html.twig", 94)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 95
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 95, $this->source); })()), "rows" =>         // line 96
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 96, $this->source); })()), "empty_message" => (("No remote courses found. <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_remote_course_create")) . "\" class=\"text-primary\">Create your first remote course</a>.")]));
        // line 99
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/remote_courses/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  653 => 99,  651 => 96,  650 => 95,  649 => 94,  646 => 93,  640 => 92,  637 => 87,  635 => 86,  633 => 85,  631 => 84,  629 => 83,  627 => 80,  626 => 79,  625 => 78,  624 => 77,  623 => 76,  622 => 75,  621 => 74,  620 => 73,  618 => 71,  613 => 70,  611 => 69,  608 => 68,  606 => 57,  603 => 56,  590 => 55,  567 => 54,  268 => 194,  258 => 187,  227 => 159,  217 => 152,  164 => 101,  162 => 54,  159 => 53,  157 => 46,  156 => 39,  155 => 32,  154 => 25,  153 => 13,  140 => 12,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Remote Courses Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Remote Courses Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Remote Courses</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Remote Courses Management',
    'page_icon': 'fas fa-video',
    'search_placeholder': 'Search remote courses...',
    'create_button': {
        'url': path('admin_remote_course_create'),
        'text': 'Add New Remote Course',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Remote Courses',
            'value': stats.total,
            'icon': 'fas fa-video',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active Courses',
            'value': stats.active,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Total Enrollments',
            'value': stats.total_enrollments,
            'icon': 'fas fa-users',
            'color': '#17a2b8',
            'gradient': 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)'
        },
        {
            'title': 'Recent (30 days)',
            'value': courses|filter(course => course.createdAt and course.createdAt > date('-30 days'))|length,
            'icon': 'fas fa-clock',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Code'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Instructor'},
            {'text': 'Chapters'},
            {'text': 'Videos'},
            {'text': 'Price'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 250px;'}
        ] %}

        {% set table_rows = [] %}
        {% for course in courses %}
            {% set table_rows = table_rows|merge([{
                'cells': [
                    {'content': '<span class=\"badge bg-primary\">' ~ course.code ~ '</span>'},
                    {'content': '<strong>' ~ course.title ~ '</strong>'},
                    {'content': course.category ?: '<span class=\"text-muted\">Not specified</span>'},
                    {'content': course.instructor ?: '<span class=\"text-muted\">Not assigned</span>'},
                    {'content': '<span class=\"badge bg-secondary\">' ~ course.chapters.count ~ ' chapters</span>'},
                    {'content': '<span class=\"badge bg-info\">' ~ course.totalVideos ~ ' videos</span>'},
                    {'content': '<strong>' ~ course.formattedPrice ~ '</strong>'},
                    {'content': course.statusBadge|raw},
                    {
                        'content': '<div class=\"btn-group\" role=\"group\">
                            <a href=\"' ~ path('admin_remote_course_preview', {'id': course.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Preview Remote Course\"><i class=\"fas fa-eye\"></i></a>
                            <a href=\"' ~ path('admin_remote_course_edit', {'id': course.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #007bff; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Remote Course\"><i class=\"fas fa-edit\"></i></a>
                            <a href=\"' ~ path('admin_remote_course_chapters', {'id': course.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #6f42c1; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Manage Chapters\"><i class=\"fas fa-list\"></i></a>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (course.isActive ? '#6c757d' : '#28a745') ~ '; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (course.isActive ? 'Deactivate' : 'Activate') ~ ' Remote Course\" onclick=\"toggleRemoteCourseStatus(' ~ course.id ~ ', \\'' ~ course.title ~ '\\', ' ~ course.isActive ~ ')\"><i class=\"fas fa-' ~ (course.isActive ? 'pause' : 'play') ~ '\"></i></button>
                            <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #a90418; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Remote Course\" onclick=\"deleteRemoteCourse(' ~ course.id ~ ', \\'' ~ course.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                        </div>'
                    }
                ]
            }]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'empty_message': 'No remote courses found. <a href=\"' ~ path('admin_remote_course_create') ~ '\" class=\"text-primary\">Create your first remote course</a>.'
        } %}
    {% endblock %}
{% endembed %}

<!-- Status Toggle Modal -->
<div class=\"modal fade\" id=\"statusToggleModal\" tabindex=\"-1\" aria-labelledby=\"statusToggleModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"statusToggleModalLabel\">Confirm Status Change</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to <span id=\"statusAction\"></span> the remote course \"<span id=\"statusCourseTitle\"></span>\"?</p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-primary\" id=\"confirmStatusToggle\">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class=\"modal fade\" id=\"deleteModal\" tabindex=\"-1\" aria-labelledby=\"deleteModalLabel\" aria-hidden=\"true\">
    <div class=\"modal-dialog\">
        <div class=\"modal-content\">
            <div class=\"modal-header\">
                <h5 class=\"modal-title\" id=\"deleteModalLabel\">Confirm Deletion</h5>
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
            </div>
            <div class=\"modal-body\">
                <p>Are you sure you want to delete the remote course \"<span id=\"deleteCourseTitle\"></span>\"?</p>
                <p class=\"text-danger\"><strong>Warning:</strong> This action cannot be undone. All chapters and video assignments will be permanently deleted.</p>
            </div>
            <div class=\"modal-footer\">
                <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
                <button type=\"button\" class=\"btn btn-danger\" id=\"confirmDelete\">Delete Remote Course</button>
            </div>
        </div>
    </div>
</div>

<script>
// Status toggle functionality
function toggleRemoteCourseStatus(courseId, courseTitle, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('statusCourseTitle').textContent = courseTitle;

    const modal = new bootstrap.Modal(document.getElementById('statusToggleModal'));
    modal.show();

    document.getElementById('confirmStatusToggle').onclick = function() {
        fetch(`{{ path('admin_remote_course_toggle_status', {'id': '__ID__'}) }}`.replace('__ID__', courseId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                '_token': '{{ csrf_token('toggle_status') }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to update course status'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the course status');
        });

        modal.hide();
    };
}

// Delete functionality
function deleteRemoteCourse(courseId, courseTitle) {
    document.getElementById('deleteCourseTitle').textContent = courseTitle;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();

    document.getElementById('confirmDelete').onclick = function() {
        fetch(`{{ path('admin_remote_course_delete', {'id': '__ID__'}) }}`.replace('__ID__', courseId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                '_token': '{{ csrf_token('delete') }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to delete course'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the course');
        });

        modal.hide();
    };
}
</script>
{% endblock %}
", "admin/remote_courses/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\remote_courses\\index.html.twig");
    }
}
