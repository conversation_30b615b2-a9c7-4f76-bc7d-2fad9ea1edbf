<?php

namespace App\Entity;

use App\Repository\OnsiteCourseRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OnsiteCourseRepository::class)]
#[ORM\Table(name: 'onsite_course')]
class OnsiteCourse
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 10)]
    private ?string $code = null;

    #[ORM\Column(length: 255)]
    private ?string $title = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(length: 100)]
    private ?string $category = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $thumbnail_image = null;

    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $learning_outcomes = [];

    #[ORM\Column(type: 'json', nullable: true)]
    private ?array $features = [];

    #[ORM\Column]
    private int $view_count = 0;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $level = null;

    #[ORM\Column]
    private ?bool $is_active = true;

    #[ORM\Column]
    private bool $has_modules = false;

    #[ORM\Column]
    private ?\DateTimeImmutable $created_at = null;

    /**
     * @var Collection<int, OnsiteCourseModule>
     */
    #[ORM\OneToMany(targetEntity: OnsiteCourseModule::class, mappedBy: 'onsiteCourse', orphanRemoval: true)]
    private Collection $modules;

    public function __construct()
    {
        $this->modules = new ArrayCollection();
        $this->created_at = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(string $category): static
    {
        $this->category = $category;

        return $this;
    }

    public function getThumbnailImage(): ?string
    {
        return $this->thumbnail_image;
    }

    public function setThumbnailImage(?string $thumbnail_image): static
    {
        $this->thumbnail_image = $thumbnail_image;

        return $this;
    }

    public function getLearningOutcomes(): ?array
    {
        return $this->learning_outcomes;
    }

    public function setLearningOutcomes(?array $learning_outcomes): static
    {
        $this->learning_outcomes = $learning_outcomes;

        return $this;
    }

    public function getFeatures(): ?array
    {
        return $this->features;
    }

    public function setFeatures(?array $features): static
    {
        $this->features = $features;

        return $this;
    }

    public function getViewCount(): int
    {
        return $this->view_count;
    }

    public function setViewCount(int $view_count): static
    {
        $this->view_count = $view_count;

        return $this;
    }

    public function incrementViewCount(): static
    {
        $this->view_count++;
        return $this;
    }

    public function getLevel(): ?string
    {
        return $this->level;
    }

    public function setLevel(?string $level): static
    {
        $this->level = $level;

        return $this;
    }



    public function isActive(): ?bool
    {
        return $this->is_active;
    }

    public function setActive(bool $is_active): static
    {
        $this->is_active = $is_active;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->created_at;
    }

    public function setCreatedAt(\DateTimeImmutable $created_at): static
    {
        $this->created_at = $created_at;

        return $this;
    }



    /**
     * @return Collection<int, OnsiteCourseModule>
     */
    public function getModules(): Collection
    {
        return $this->modules;
    }

    public function addModule(OnsiteCourseModule $module): static
    {
        if (!$this->modules->contains($module)) {
            $this->modules->add($module);
            $module->setOnsiteCourse($this);
        }

        return $this;
    }

    public function removeModule(OnsiteCourseModule $module): static
    {
        if ($this->modules->removeElement($module)) {
            // set the owning side to null (unless already changed)
            if ($module->getOnsiteCourse() === $this) {
                $module->setOnsiteCourse(null);
            }
        }

        return $this;
    }

    public function hasModules(): bool
    {
        return $this->has_modules;
    }

    public function setHasModules(bool $hasModules): static
    {
        $this->has_modules = $hasModules;
        return $this;
    }

    /**
     * Get thumbnail image URL with fallback
     */
    public function getThumbnailUrl(): string
    {
        if ($this->thumbnail_image) {
            return '/uploads/courses/' . $this->thumbnail_image;
        }
        return '/images/course-placeholder.jpg';
    }


}
