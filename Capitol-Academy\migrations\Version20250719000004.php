<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Clean up onsite course and onsite course module tables
 * Remove unwanted columns and rename columns for consistency
 */
final class Version20250719000004 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Clean up onsite course tables - remove unwanted columns and rename for consistency';
    }

    public function up(Schema $schema): void
    {
        // Clean up onsite_course table
        $this->addSql('ALTER TABLE onsite_course DROP COLUMN IF EXISTS duration');
        $this->addSql('ALTER TABLE onsite_course DROP COLUMN IF EXISTS price');
        $this->addSql('ALTER TABLE onsite_course DROP COLUMN IF EXISTS banner_image');
        $this->addSql('ALTER TABLE onsite_course DROP COLUMN IF EXISTS enrolled_count');
        $this->addSql('ALTER TABLE onsite_course DROP COLUMN IF EXISTS active_enrollments');
        $this->addSql('ALTER TABLE onsite_course DROP COLUMN IF EXISTS completed_count');
        $this->addSql('ALTER TABLE onsite_course DROP COLUMN IF EXISTS certified_count');
        $this->addSql('ALTER TABLE onsite_course DROP COLUMN IF EXISTS average_rating');
        $this->addSql('ALTER TABLE onsite_course DROP COLUMN IF EXISTS total_reviews');
        $this->addSql('ALTER TABLE onsite_course DROP COLUMN IF EXISTS updated_at');
        
        // Rename hasModules to has_modules for consistency
        $this->addSql('ALTER TABLE onsite_course CHANGE hasModules has_modules TINYINT(1) NOT NULL');

        // Clean up onsite_course_module table
        $this->addSql('ALTER TABLE onsite_course_module DROP COLUMN IF EXISTS displayOrder');
        $this->addSql('ALTER TABLE onsite_course_module DROP COLUMN IF EXISTS duration');
        $this->addSql('ALTER TABLE onsite_course_module DROP COLUMN IF EXISTS price');
        $this->addSql('ALTER TABLE onsite_course_module DROP COLUMN IF EXISTS updatedAt');
        
        // Rename createdAt to created_at for consistency
        $this->addSql('ALTER TABLE onsite_course_module CHANGE createdAt created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
    }

    public function down(Schema $schema): void
    {
        // Restore onsite_course table columns
        $this->addSql('ALTER TABLE onsite_course ADD duration INT DEFAULT NULL');
        $this->addSql('ALTER TABLE onsite_course ADD price NUMERIC(10, 2) NOT NULL DEFAULT \'0.00\'');
        $this->addSql('ALTER TABLE onsite_course ADD banner_image VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE onsite_course ADD enrolled_count INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE onsite_course ADD active_enrollments INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE onsite_course ADD completed_count INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE onsite_course ADD certified_count INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE onsite_course ADD average_rating NUMERIC(3, 2) DEFAULT \'0.00\'');
        $this->addSql('ALTER TABLE onsite_course ADD total_reviews INT NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE onsite_course ADD updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        
        // Revert hasModules column name
        $this->addSql('ALTER TABLE onsite_course CHANGE has_modules hasModules TINYINT(1) NOT NULL');

        // Restore onsite_course_module table columns
        $this->addSql('ALTER TABLE onsite_course_module ADD displayOrder INT DEFAULT NULL');
        $this->addSql('ALTER TABLE onsite_course_module ADD duration INT DEFAULT NULL');
        $this->addSql('ALTER TABLE onsite_course_module ADD price NUMERIC(10, 2) DEFAULT \'0.00\'');
        $this->addSql('ALTER TABLE onsite_course_module ADD updatedAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        
        // Revert createdAt column name
        $this->addSql('ALTER TABLE onsite_course_module CHANGE created_at createdAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
    }
}
