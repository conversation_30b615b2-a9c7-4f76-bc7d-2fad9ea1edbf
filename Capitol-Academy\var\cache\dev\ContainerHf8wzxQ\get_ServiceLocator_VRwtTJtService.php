<?php

namespace ContainerHf8wzxQ;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_VRwtTJtService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.vRwtTJt' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.vRwtTJt'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'cartService' => ['privates', 'App\\Service\\CartService', 'getCartServiceService', true],
            'courseRepository' => ['privates', '.errored.PfFB_mP', NULL, 'Cannot determine controller argument for "App\\Controller\\CourseController::addToCart()": the $courseRepository argument is type-hinted with the non-existent class or interface: "App\\Controller\\CourseRepository". Did you forget to add a use statement?'],
        ], [
            'cartService' => 'App\\Service\\CartService',
            'courseRepository' => '?',
        ]);
    }
}
