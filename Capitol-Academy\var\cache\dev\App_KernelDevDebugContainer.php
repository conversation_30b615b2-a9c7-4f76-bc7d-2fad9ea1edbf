<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerHf8wzxQ\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerHf8wzxQ/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerHf8wzxQ.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerHf8wzxQ\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerHf8wzxQ\App_KernelDevDebugContainer([
    'container.build_hash' => 'Hf8wzxQ',
    'container.build_id' => 'fffbefdb',
    'container.build_time' => 1752921387,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerHf8wzxQ');
