<?php

namespace ContainerHf8wzxQ;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getRemoteCourseRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\RemoteCourseRepository' shared autowired service.
     *
     * @return \App\Repository\RemoteCourseRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'RemoteCourseRepository.php';

        return $container->privates['App\\Repository\\RemoteCourseRepository'] = new \App\Repository\RemoteCourseRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
