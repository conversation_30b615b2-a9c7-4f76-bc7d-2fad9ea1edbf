<?php

namespace ContainerHf8wzxQ;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getErrorHandlingServiceService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Service\ErrorHandlingService' shared autowired service.
     *
     * @return \App\Service\ErrorHandlingService
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'ErrorHandlingService.php';

        return $container->privates['App\\Service\\ErrorHandlingService'] = new \App\Service\ErrorHandlingService(($container->privates['monolog.logger'] ?? self::getMonolog_LoggerService($container)), ($container->privates['App\\Service\\IpAddressService'] ?? $container->load('getIpAddressServiceService')));
    }
}
